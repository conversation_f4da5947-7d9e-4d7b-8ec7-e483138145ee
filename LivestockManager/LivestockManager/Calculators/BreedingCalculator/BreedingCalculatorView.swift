import SwiftUI

// 繁殖计算器视图
struct BreedingCalculatorView: View {
    @State private var selectedAnimalType: String = "cattle"
    @State private var breedingDate = Date()
    @State private var expectedDueDate = Date()
    @State private var remainingDays: Int = 0
    @State private var gestationProgress: Double = 0
    @State private var keyDates: [KeyDate] = []
    
    let animalTypes = [
        ("cattle", L.Tools.Calculator.Animal.cattle, "🐄", 283),
        ("sheep", L.Tools.Calculator.Animal.sheep, "🐑", 152),
        ("pig", L.Tools.Calculator.Animal.pig, "🐷", 114),
        ("horse", L.Tools.Calculator.Animal.horse, "🐎", 340),
        ("goat", L.Tools.Calculator.Animal.goat, "🐐", 150)
    ]
    
    struct KeyDate: Identifiable {
        let id = UUID()
        let title: String
        let date: Date
        let description: String
        let icon: String
    }
    
    var body: some View {
        VStack(spacing: 24) {
            // 标题和说明
            VStack(alignment: .leading, spacing: 8) {
                Text(L.Tools.Calculator.breedingTitle)
                    .font(.system(size: 20, weight: .semibold))
                    .foregroundColor(.black)

                Text(L.Tools.Calculator.breedingDescription)
                    .font(.system(size: 14))
                    .foregroundColor(Color.black.opacity(0.6))
            }
            .frame(maxWidth: .infinity, alignment: .leading)
            
            // 参数输入卡片
            VStack(spacing: 20) {
                // 动物类型选择
                VStack(alignment: .leading, spacing: 8) {
                    Text(L.Tools.Calculator.animalType)
                        .font(.system(size: 16, weight: .semibold))
                        .foregroundColor(.black)
                    
                    ScrollView(.horizontal, showsIndicators: false) {
                        HStack(spacing: 12) {
                            ForEach(animalTypes, id: \.0) { type in
                                AnimalTypeButton(
                                    icon: type.2,
                                    name: type.1,
                                    isSelected: selectedAnimalType == type.0,
                                    action: {
                                        selectedAnimalType = type.0
                                        calculateBreeding()
                                    }
                                )
                            }
                        }
                    }
                }
                
                // 繁殖日期选择
                VStack(alignment: .leading, spacing: 8) {
                    Text(L.Tools.Calculator.breedingDate)
                        .font(.system(size: 16, weight: .semibold))
                        .foregroundColor(.black)
                    
                    DatePicker("", selection: $breedingDate, displayedComponents: .date)
                        .datePickerStyle(WheelDatePickerStyle())
                        .labelsHidden()
                        .frame(maxWidth: .infinity)
                        .onChange(of: breedingDate) { _ in
                            calculateBreeding()
                        }
                }
            }
            .padding(16)
            .background(Color.white)
            .cornerRadius(16)
            .overlay(
                RoundedRectangle(cornerRadius: 16)
                    .stroke(Color.black.opacity(0.08), lineWidth: 1)
            )
            
            // 结果卡片
            VStack(spacing: 20) {
                // 预产期
                VStack(spacing: 12) {
                    Text(L.Tools.Calculator.dueDate)
                        .font(.system(size: 18, weight: .semibold))
                        .foregroundColor(.black)

                    Text(formatDate(expectedDueDate))
                        .font(.system(size: 28, weight: .bold))
                        .foregroundColor(.black)

                    HStack(spacing: 16) {
                        // 剩余天数
                        VStack {
                            Text("\(remainingDays)")
                                .font(.system(size: 22, weight: .semibold))
                                .foregroundColor(.black)

                            Text(L.Tools.Calculator.remainingDays)
                                .font(.system(size: 14))
                                .foregroundColor(Color.black.opacity(0.6))
                        }
                        .frame(maxWidth: .infinity)

                        // 妊娠进度
                        VStack {
                            Text(String(format: "%.0f%%", gestationProgress * 100))
                                .font(.system(size: 22, weight: .semibold))
                                .foregroundColor(.black)

                            Text(L.Tools.Calculator.gestationProgress)
                                .font(.system(size: 14))
                                .foregroundColor(Color.black.opacity(0.6))
                        }
                        .frame(maxWidth: .infinity)
                    }
                    .padding(.top, 8)
                    
                    // 进度条
                    VStack(alignment: .leading, spacing: 8) {
                        GeometryReader { geometry in
                            ZStack(alignment: .leading) {
                                // 背景条
                                RoundedRectangle(cornerRadius: 4)
                                    .fill(Color.black.opacity(0.1))
                                    .frame(height: 8)
                                
                                // 进度条
                                RoundedRectangle(cornerRadius: 4)
                                    .fill(Color.black)
                                    .frame(width: geometry.size.width * gestationProgress, height: 8)
                            }
                        }
                        .frame(height: 8)
                    }
                    .padding(.top, 8)
                }
                
                // 关键日期
                VStack(alignment: .leading, spacing: 16) {
                    Text(L.Tools.Calculator.keyDates)
                        .font(.system(size: 16, weight: .semibold))
                        .foregroundColor(.black)
                    
                    ForEach(keyDates) { keyDate in
                        HStack(spacing: 16) {
                            // 图标
                            Text(keyDate.icon)
                                .font(.system(size: 24))
                                .frame(width: 40, height: 40)
                                .background(Color.black.opacity(0.03))
                                .cornerRadius(20)
                            
                            VStack(alignment: .leading, spacing: 4) {
                                HStack {
                                    Text(keyDate.title)
                                        .font(.system(size: 16, weight: .medium))
                                        .foregroundColor(.black)
                                    
                                    Spacer()
                                    
                                    Text(formatDate(keyDate.date))
                                        .font(.system(size: 14))
                                        .foregroundColor(Color.black.opacity(0.7))
                                }
                                
                                Text(keyDate.description)
                                    .font(.system(size: 14))
                                    .foregroundColor(Color.black.opacity(0.6))
                                    .lineLimit(2)
                            }
                        }
                        .padding(12)
                        .background(Color.white)
                        .cornerRadius(12)
                        .overlay(
                            RoundedRectangle(cornerRadius: 12)
                                .stroke(Color.black.opacity(0.05), lineWidth: 1)
                        )
                    }
                }
            }
            .padding(16)
            .background(Color.white)
            .cornerRadius(16)
            .overlay(
                RoundedRectangle(cornerRadius: 16)
                    .stroke(Color.black.opacity(0.08), lineWidth: 1)
            )
            
            // 繁殖提示卡片
            VStack(alignment: .leading, spacing: 12) {
                Text("繁殖管理提示")
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(.black)
                
                Text(getBreedingAdvice())
                    .font(.system(size: 14))
                    .foregroundColor(Color.black.opacity(0.7))
                    .lineSpacing(4)
            }
            .padding(16)
            .frame(maxWidth: .infinity, alignment: .leading)
            .background(Color.white)
            .cornerRadius(16)
            .overlay(
                RoundedRectangle(cornerRadius: 16)
                    .stroke(Color.black.opacity(0.08), lineWidth: 1)
            )
        }
        .onAppear {
            calculateBreeding()
        }
    }
    
    // 计算繁殖相关日期
    private func calculateBreeding() {
        // 获取当前选择的动物类型的妊娠天数
        let gestationDays = getGestationDays()
        
        // 计算预产期
        let calendar = Calendar.current
        if let dueDate = calendar.date(byAdding: .day, value: gestationDays, to: breedingDate) {
            expectedDueDate = dueDate
            
            // 计算剩余天数
            let components = calendar.dateComponents([.day], from: Date(), to: dueDate)
            remainingDays = max(0, components.day ?? 0)
            
            // 计算妊娠进度
            let elapsedDays = calendar.dateComponents([.day], from: breedingDate, to: Date()).day ?? 0
            gestationProgress = min(1.0, max(0.0, Double(elapsedDays) / Double(gestationDays)))
            
            // 生成关键日期
            generateKeyDates(gestationDays: gestationDays)
        }
    }
    
    // 获取妊娠天数
    private func getGestationDays() -> Int {
        for type in animalTypes {
            if type.0 == selectedAnimalType {
                return type.3
            }
        }
        return 280 // 默认值
    }
    
    // 生成关键日期
    private func generateKeyDates(gestationDays: Int) {
        let calendar = Calendar.current
        var newKeyDates: [KeyDate] = []
        
        // 繁殖日期
        newKeyDates.append(KeyDate(
            title: "繁殖日期",
            date: breedingDate,
            description: "动物交配或人工授精的日期",
            icon: "🔄"
        ))
        
        switch selectedAnimalType {
        case "cattle":
            // 牛的关键日期
            if let confirmationDate = calendar.date(byAdding: .day, value: 45, to: breedingDate) {
                newKeyDates.append(KeyDate(
                    title: "妊娠确认",
                    date: confirmationDate,
                    description: "建议进行妊娠检查的时间",
                    icon: "✅"
                ))
            }
            
            if let dryPeriodDate = calendar.date(byAdding: .day, value: gestationDays - 60, to: breedingDate) {
                newKeyDates.append(KeyDate(
                    title: "干奶期开始",
                    date: dryPeriodDate,
                    description: "停止挤奶，让母牛为产犊做准备",
                    icon: "🥛"
                ))
            }
            
        case "sheep", "goat":
            // 羊的关键日期
            if let confirmationDate = calendar.date(byAdding: .day, value: 35, to: breedingDate) {
                newKeyDates.append(KeyDate(
                    title: "妊娠确认",
                    date: confirmationDate,
                    description: "建议进行妊娠检查的时间",
                    icon: "✅"
                ))
            }
            
        case "pig":
            // 猪的关键日期
            if let confirmationDate = calendar.date(byAdding: .day, value: 21, to: breedingDate) {
                newKeyDates.append(KeyDate(
                    title: "妊娠确认",
                    date: confirmationDate,
                    description: "建议进行妊娠检查的时间",
                    icon: "✅"
                ))
            }
            
            if let nestingDate = calendar.date(byAdding: .day, value: gestationDays - 7, to: breedingDate) {
                newKeyDates.append(KeyDate(
                    title: "准备产床",
                    date: nestingDate,
                    description: "为母猪准备产床和产仔环境",
                    icon: "🏠"
                ))
            }
            
        case "horse":
            // 马的关键日期
            if let confirmationDate = calendar.date(byAdding: .day, value: 42, to: breedingDate) {
                newKeyDates.append(KeyDate(
                    title: "妊娠确认",
                    date: confirmationDate,
                    description: "建议进行妊娠检查的时间",
                    icon: "✅"
                ))
            }
            
            if let vaccinationDate = calendar.date(byAdding: .day, value: gestationDays - 45, to: breedingDate) {
                newKeyDates.append(KeyDate(
                    title: "疫苗接种",
                    date: vaccinationDate,
                    description: "为孕马接种必要的疫苗",
                    icon: "💉"
                ))
            }
        default:
            // 其他动物类型的默认关键日期
            if let confirmationDate = calendar.date(byAdding: .day, value: Int(Double(gestationDays) * 0.15), to: breedingDate) {
                newKeyDates.append(KeyDate(
                    title: "妊娠确认",
                    date: confirmationDate,
                    description: "建议进行妊娠检查的时间",
                    icon: "✅"
                ))
            }
        }
        
        // 所有动物都有的预产期
        newKeyDates.append(KeyDate(
            title: "预产期",
            date: expectedDueDate,
            description: "预计分娩的日期",
            icon: "👶"
        ))
        
        // 按日期排序
        keyDates = newKeyDates.sorted { $0.date < $1.date }
    }
    
    // 获取繁殖建议
    private func getBreedingAdvice() -> String {
        switch selectedAnimalType {
        case "cattle":
            return "牛的妊娠期约为283天（9.5个月）。在妊娠最后两个月需要特别注意营养补充，准备一个干净、宽敞的产犊区域。产前应停止挤奶（干奶期），让母牛的身体为产犊和新的泌乳周期做准备。"
        case "sheep":
            return "羊的妊娠期约为152天（5个月）。妊娠后期需要增加能量和蛋白质摄入。羊通常能够自己分娩，但应准备好干净的羊圈和必要的助产工具，以防需要协助。"
        case "pig":
            return "猪的妊娠期约为114天（3个月3周3天）。妊娠后期应确保母猪获得足够的能量和蛋白质。分娩前一周准备好干净、温暖的产床和保温灯，以确保新生仔猪的舒适。"
        case "horse":
            return "马的妊娠期约为340天（11个月）。妊娠马需要适当的运动和均衡的饮食。分娩前几周应密切观察马匹，准备干净的马厩，并确保兽医随时可以联系到，因为马的分娩过程需要特别关注。"
        case "goat":
            return "山羊的妊娠期约为150天（5个月）。妊娠后期需要增加能量摄入，减少压力。山羊通常能够自行分娩，但应准备好干净的羊圈和必要的助产工具，以便在需要时提供帮助。"
        default:
            return "请根据动物的具体品种和状况，咨询兽医获取专业的繁殖管理建议。在妊娠期间提供均衡的营养，减少应激，并为即将到来的分娩做好准备。"
        }
    }
    
    // 格式化日期
    private func formatDate(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy年MM月dd日"
        return formatter.string(from: date)
    }
} 